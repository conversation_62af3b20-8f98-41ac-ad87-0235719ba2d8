<script setup lang="ts">
import { computed, inject, ref, watchEffect, onMounted } from 'vue'
import { injectKeyProps } from '../types'
import { injectRefListToSFC } from '../transform'

const props = defineProps<{
  show: boolean
  refList?: Array<any> | Object
  dataChart?: Object
  styleChartName?: Object
}>()

const { store } = inject(injectKeyProps)!

// 响应式数据
const processedCode = ref<string>('')
const compilationError = ref<string>('')

// 获取处理后的代码
const updateProcessedCode = () => {
  try {
    const activeFile = store.value.activeFile
    if (!activeFile || !activeFile.filename.endsWith('.vue')) {
      processedCode.value = ''
      compilationError.value = ''
      return
    }

    // 使用 injectRefListToSFC 处理代码
    const injectedCode = injectRefListToSFC(activeFile.code, activeFile.filename)
    processedCode.value = injectedCode
    compilationError.value = ''

    console.log('Preview2 - Original code:', activeFile.code)
    console.log('Preview2 - Processed code:', injectedCode)

  } catch (error: any) {
    console.error('Code processing error:', error)
    compilationError.value = error.message || 'Unknown processing error'
    processedCode.value = ''
  }
}

// 计算动态组件
const dynamicComponent = computed(() => {
  const activeFile = store.value.activeFile
  if (!activeFile || !activeFile.filename.endsWith('.vue')) {
    return null
  }

  // 简化处理：直接返回文件名作为组件标识
  // 这将在模板中被解析为实际的组件
  return activeFile.filename
})

// 监听文件变化
watchEffect(() => {
  if (props.show && store.value.activeFile) {
    updateProcessedCode()
  }
})

onMounted(() => {
  if (props.show) {
    updateProcessedCode()
  }
})
</script>

<template>
  <div v-if="props.show" class="preview2-container">
    <div class="header">
      <h3>Preview2 - Component with Injected Props</h3>
      <p class="description">This preview shows the component after injectRefListToSFC processing</p>
    </div>

    <div v-if="compilationError" class="error-message">
      <h4>Processing Error:</h4>
      <pre>{{ compilationError }}</pre>
    </div>

    <div v-else-if="processedCode" class="content">
      <div class="code-section">
        <h4>Processed Code:</h4>
        <pre class="code-display">{{ processedCode }}</pre>
      </div>

      <div v-if="dynamicComponent" class="component-info">
        <h4>Component Information:</h4>
        <div class="info-wrapper">
          <p><strong>Active File:</strong> {{ dynamicComponent }}</p>
          <p><strong>Props Injected:</strong> dataChart, refList</p>
          <p><strong>Status:</strong> Ready for rendering with injected props</p>
          <div class="note">
            <p>
              <strong>Note:</strong> This preview shows the code after injectRefListToSFC processing.
              The actual component rendering happens in the main preview tab.
            </p>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="no-component">
      <p>No Vue component to preview</p>
    </div>
  </div>
</template>

<style scoped>
.preview2-container {
  width: 100%;
  height: 100%;
  padding: 16px;
  box-sizing: border-box;
  overflow: auto;
}

.header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.header h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.description {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.code-section, .component-info {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
}

.code-section h4, .component-info h4 {
  margin: 0;
  padding: 8px 12px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-size: 12px;
  font-weight: 600;
  color: #374151;
}

.code-display {
  margin: 0;
  padding: 12px;
  font-size: 11px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-word;
  background: #f8fafc;
  color: #1e293b;
  max-height: 300px;
  overflow: auto;
}

.info-wrapper {
  padding: 12px;
  background: white;
}

.info-wrapper p {
  margin: 8px 0;
  font-size: 13px;
  line-height: 1.4;
}

.info-wrapper strong {
  color: #374151;
  font-weight: 600;
}

.note {
  margin-top: 16px;
  padding: 12px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 4px;
}

.note p {
  margin: 0;
  font-size: 12px;
  color: #0369a1;
  line-height: 1.5;
}

.error-message {
  color: #dc2626;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
}

.error-message h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
}

.error-message pre {
  margin: 0;
  font-size: 12px;
  white-space: pre-wrap;
  word-break: break-word;
}

.no-component {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6b7280;
  font-style: italic;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}
</style>
