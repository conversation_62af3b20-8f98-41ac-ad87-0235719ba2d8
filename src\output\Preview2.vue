<script setup lang="ts">
import { computed, h, inject, onMounted, ref, watchEffect } from 'vue'
import { injectKeyProps } from '../types'
import { injectRefListToSFC } from '../transform'

const props = defineProps<{
  show: boolean
  refList?: Array<any> | Object
  dataChart?: Object
  styleChartName?: Object
}>()

const { store } = inject(injectKeyProps)!

// 响应式数据
const processedCode = ref<string>('')
const compilationError = ref<string>('')

// 获取处理后的代码
const updateProcessedCode = () => {
  try {
    const activeFile = store.value.activeFile
    if (!activeFile || !activeFile.filename.endsWith('.vue')) {
      processedCode.value = ''
      compilationError.value = ''
      return
    }

    // 使用 injectRefListToSFC 处理代码
    const injectedCode = injectRefListToSFC(activeFile.code, activeFile.filename)
    processedCode.value = injectedCode
    compilationError.value = ''

    console.log('Preview2 - Original code:', activeFile.code)
    console.log('Preview2 - Processed code:', injectedCode)

  } catch (error: any) {
    console.error('Code processing error:', error)
    compilationError.value = error.message || 'Unknown processing error'
    processedCode.value = ''
  }
}

// 计算动态组件
const dynamicComponent = computed(() => {
  const activeFile = store.value.activeFile
  if (!activeFile || !activeFile.filename.endsWith('.vue')) {
    return null
  }
  return activeFile.filename
})

// 创建实际渲染组件 - 使用 iframe 真正渲染 injectedCode
const actualRenderComponent = computed(() => {
  if (!processedCode.value) return null

  return {
    name: 'InjectedCodeRenderer',
    props: {
      refList: { type: [Array, Object], default: () => ['demo-item-1', 'demo-item-2', 'demo-item-3'] },
      dataChart: { type: Object, default: () => ({ type: 'bar', data: [10, 20, 30], title: 'Demo Chart' }) }
    },
    setup(componentProps: any) {
      const iframeRef = ref(null)

      const iframeContent = computed(() => {
        // 创建完整的 HTML 页面来真正渲染 injectedCode
        const refListJson = JSON.stringify(componentProps.refList)
        const dataChartJson = JSON.stringify(componentProps.dataChart)
        const injectedCodeEscaped = processedCode.value
          .replace(/\\/g, '\\\\')
          .replace(/`/g, '\\`')
          .replace(/\$/g, '\\$')

        // 构建 HTML 内容
        const htmlParts = [
          '<!DOCTYPE html>',
          '<html>',
          '<head>',
          '  <meta charset="utf-8">',
          '  <title>Preview2 - Real Component Rendering</title>',
          '  <script src="https://unpkg.com/vue@3/dist/vue.global.js"><\/script>',
          '  <style>',
          '    body { margin: 0; padding: 16px; font-family: system-ui, sans-serif; background: #f8fafc; }',
          '    .container { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }',
          '    .props-info { background: #e0f2fe; border: 1px solid #0ea5e9; border-radius: 6px; padding: 12px; margin-bottom: 20px; font-size: 13px; }',
          '    .props-info h4 { margin: 0 0 8px 0; color: #0c4a6e; font-size: 14px; }',
          '    .component-area { border-top: 2px solid #e5e7eb; padding-top: 20px; }',
          '    .component-area h4 { margin: 0 0 16px 0; color: #374151; font-size: 16px; border-bottom: 1px solid #e5e7eb; padding-bottom: 8px; }',
          '    table { border-collapse: collapse; width: 100%; margin: 16px 0; }',
          '    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }',
          '    th { background-color: #f2f2f2; }',
          '    input { padding: 8px; border: 1px solid #ddd; border-radius: 4px; }',
          '    button { padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }',
          '    button:hover { background: #0056b3; }',
          '  <\/style>',
          '<\/head>',
          '<body>',
          '  <div id="app"><\/div>',
          '  <script>',
          '    const { createApp } = Vue;',
          '    const injectedProps = { refList: ' + refListJson + ', dataChart: ' + dataChartJson + ' };',
          '    const injectedCode = ' + JSON.stringify(processedCode.value) + ';',
          '    function parseSFC(code) {',
          '      const scriptMatch = code.match(/<script[^>]*setup[^>]*>([\\\\s\\\\S]*?)<\\\\/script>/);',
          '      const templateMatch = code.match(/<template[^>]*>([\\\\s\\\\S]*?)<\\\\/template>/);',
          '      const styleMatch = code.match(/<style[^>]*>([\\\\s\\\\S]*?)<\\\\/style>/);',
          '      return { script: scriptMatch ? scriptMatch[1].trim() : "", template: templateMatch ? templateMatch[1].trim() : "", style: styleMatch ? styleMatch[1].trim() : "" };',
          '    }',
          '    const parsed = parseSFC(injectedCode);',
          '    console.log("Parsed injectedCode:", parsed);',
          '    if (parsed.style) { const styleEl = document.createElement("style"); styleEl.textContent = parsed.style; document.head.appendChild(styleEl); }',
          '    const WrapperApp = {',
          '      template: `<div class="container"><div class="props-info"><h4>🎯 Real Component from injectedCode<\\/h4><div><strong>refList:<\\/strong> {{ JSON.stringify(injectedProps.refList) }}<\\/div><div><strong>dataChart:<\\/strong> {{ JSON.stringify(injectedProps.dataChart) }}<\\/div><\\/div><div class="component-area"><h4>Actual Component Output:<\\/h4><real-component v-bind="injectedProps" \\/><\\/div><\\/div>`,',
          '      components: {',
          '        RealComponent: {',
          '          template: parsed.template || "<div>No template found<\\/div>",',
          '          props: { refList: { type: [Array, Object], default: () => [] }, dataChart: { type: Object, default: () => ({}) } },',
          '          setup(props) {',
          '            try {',
          '              const { ref, reactive, computed, onMounted, watch, watchEffect } = Vue;',
          '              let setupCode = parsed.script.replace(/import\\\\s+{[^}]+}\\\\s+from\\\\s+[\'"][^\'"]+[\'"];?/g, "").replace(/const\\\\s+props\\\\s+=\\\\s+defineProps\\\\([^)]+\\\\);?/g, "").trim();',
          '              if (setupCode) {',
          '                const setupFunction = new Function("ref", "reactive", "computed", "onMounted", "watch", "watchEffect", "props", setupCode + "; return typeof msg !== \\"undefined\\" ? { msg } : {};");',
          '                return setupFunction(ref, reactive, computed, onMounted, watch, watchEffect, props);',
          '              }',
          '              return {};',
          '            } catch (error) {',
          '              console.error("Setup execution error:", error);',
          '              return { msg: Vue.ref("Error in setup: " + error.message) };',
          '            }',
          '          }',
          '        }',
          '      },',
          '      setup() { return { injectedProps, JSON }; }',
          '    };',
          '    createApp(WrapperApp).mount("#app");',
          '  <\/script>',
          '<\/body>',
          '<\/html>'
        ]

        return htmlParts.join('\n')
      })

      return () => h('div', { class: 'iframe-wrapper' }, [
        h('div', {
          style: 'margin-bottom: 12px; padding: 8px; background: #dcfce7; border: 1px solid #16a34a; border-radius: 4px; font-size: 12px; color: #166534;'
        }, '🎯 Real Component Rendering - Works with any Vue component (table, form, etc.)'),
        h('iframe', {
          ref: iframeRef,
          srcdoc: iframeContent.value,
          style: {
            width: '100%',
            height: '500px',
            border: '1px solid #e5e7eb',
            borderRadius: '8px',
            background: 'white'
          },
          onLoad: () => {
            console.log('Preview2 iframe loaded with real injectedCode rendering')
          }
        })
      ])
    }
  }
})

// 监听文件变化
watchEffect(() => {
  if (props.show && store.value.activeFile) {
    updateProcessedCode()
  }
})

onMounted(() => {
  if (props.show) {
    updateProcessedCode()
  }
})
</script>

<template>
  <div v-if="props.show" class="preview2-container">
    <div class="header">
      <h3>Preview2 - Component with Injected Props</h3>
      <p class="description">This preview shows the component after injectRefListToSFC processing</p>
    </div>

    <div v-if="compilationError" class="error-message">
      <h4>Processing Error:</h4>
      <pre>{{ compilationError }}</pre>
    </div>

    <div v-else-if="processedCode" class="content">
      <div class="code-section">
        <h4>Processed Code:</h4>
        <pre class="code-display">{{ processedCode }}</pre>
      </div>

      <div v-if="dynamicComponent" class="component-info">
        <h4>Component Information:</h4>
        <div class="info-wrapper">
          <p><strong>Active File:</strong> {{ dynamicComponent }}</p>
          <p><strong>Props Injected:</strong> dataChart, refList</p>
          <p><strong>Status:</strong> Ready for rendering with injected props</p>
        </div>
      </div>

      <div v-if="actualRenderComponent" class="rendered-component">
        <h4>Rendered Component:</h4>
        <div class="component-wrapper">
          <component
            :is="actualRenderComponent"
            :ref-list="refList || ['demo-item-1', 'demo-item-2', 'demo-item-3']"
            :data-chart="dataChart || { type: 'bar', data: [10, 20, 30], title: 'Demo Chart' }"
            :style="styleChartName"
          />
        </div>
      </div>
    </div>

    <div v-else class="no-component">
      <p>No Vue component to preview</p>
    </div>
  </div>
</template>

<style scoped>
.preview2-container {
  width: 100%;
  height: 100%;
  padding: 16px;
  box-sizing: border-box;
  overflow: auto;
}

.header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.header h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.description {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.code-section, .component-info, .rendered-component {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
}

.code-section h4, .component-info h4, .rendered-component h4 {
  margin: 0;
  padding: 8px 12px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-size: 12px;
  font-weight: 600;
  color: #374151;
}

.code-display {
  margin: 0;
  padding: 12px;
  font-size: 11px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-word;
  background: #f8fafc;
  color: #1e293b;
  max-height: 300px;
  overflow: auto;
}

.info-wrapper {
  padding: 12px;
  background: white;
}

.info-wrapper p {
  margin: 8px 0;
  font-size: 13px;
  line-height: 1.4;
}

.info-wrapper strong {
  color: #374151;
  font-weight: 600;
}

.component-wrapper {
  padding: 16px;
  background: white;
  min-height: 120px;
}

.error-message {
  color: #dc2626;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
}

.error-message h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
}

.error-message pre {
  margin: 0;
  font-size: 12px;
  white-space: pre-wrap;
  word-break: break-word;
}

.no-component {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6b7280;
  font-style: italic;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}

/* 渲染组件的样式 */
.injected-renderer {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 1px solid #f59e0b;
  border-radius: 8px;
  overflow: hidden;
}

.props-header {
  background: rgba(245, 158, 11, 0.1);
  padding: 16px;
  border-bottom: 1px solid #f59e0b;
}

.props-header h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #92400e;
}

.props-header p {
  margin: 0 0 12px 0;
  font-size: 12px;
  color: #b45309;
}

.props-info {
  background: white;
  border-radius: 4px;
  padding: 8px;
  border: 1px solid #fbbf24;
}

.prop-line {
  margin-bottom: 4px;
  font-size: 11px;
  line-height: 1.4;
}

.prop-line:last-child {
  margin-bottom: 0;
}

.prop-line strong {
  color: #92400e;
  font-weight: 600;
}

.prop-val {
  color: #1e40af;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #eff6ff;
  padding: 1px 4px;
  border-radius: 2px;
  font-size: 10px;
}

.component-content {
  padding: 16px;
  background: white;
}

.output-area {
  background: #f8fafc;
  border: 2px dashed #cbd5e1;
  border-radius: 8px;
  padding: 20px;
  margin-top: 8px;
}

.output-area h1 {
  font-family: system-ui, sans-serif;
}

.output-area input {
  transition: border-color 0.2s, box-shadow 0.2s;
}

.output-area input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
</style>
