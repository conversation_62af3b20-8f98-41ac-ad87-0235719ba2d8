<script setup lang="ts">
import { computed, h, inject, onMounted, ref, watchEffect } from 'vue'
import { injectKeyProps } from '../types'
import { injectRefListToSFC } from '../transform'

const props = defineProps<{
  show: boolean
  refList?: Array<any> | Object
  dataChart?: Object
  styleChartName?: Object
}>()

const { store } = inject(injectKeyProps)!

// 响应式数据
const processedCode = ref<string>('')
const compilationError = ref<string>('')

// 获取处理后的代码
const updateProcessedCode = () => {
  try {
    const activeFile = store.value.activeFile
    if (!activeFile || !activeFile.filename.endsWith('.vue')) {
      processedCode.value = ''
      compilationError.value = ''
      return
    }

    // 使用 injectRefListToSFC 处理代码
    const injectedCode = injectRefListToSFC(activeFile.code, activeFile.filename)
    processedCode.value = injectedCode
    compilationError.value = ''

    console.log('Preview2 - Original code:', activeFile.code)
    console.log('Preview2 - Processed code:', injectedCode)

  } catch (error: any) {
    console.error('Code processing error:', error)
    compilationError.value = error.message || 'Unknown processing error'
    processedCode.value = ''
  }
}

// 计算动态组件
const dynamicComponent = computed(() => {
  const activeFile = store.value.activeFile
  if (!activeFile || !activeFile.filename.endsWith('.vue')) {
    return null
  }
  return activeFile.filename
})

// 创建实际渲染组件
const actualRenderComponent = computed(() => {
  if (!processedCode.value) return null

  // 解析 injectedCode
  const templateMatch = processedCode.value.match(/<template[^>]*>([\s\S]*?)<\/template>/)
  const scriptMatch = processedCode.value.match(/<script[^>]*setup[^>]*>([\s\S]*?)<\/script>/)

  const templateContent = templateMatch ? templateMatch[1].trim() : ''
  const scriptContent = scriptMatch ? scriptMatch[1].trim() : ''

  // 创建组件
  return {
    name: 'InjectedCodeRenderer',
    props: {
      refList: { type: [Array, Object], default: () => ['demo-item-1', 'demo-item-2', 'demo-item-3'] },
      dataChart: { type: Object, default: () => ({ type: 'bar', data: [10, 20, 30], title: 'Demo Chart' }) }
    },
    setup(componentProps: any) {
      // 创建响应式数据
      const msg = ref('Hello World!')

      return () => h('div', { class: 'injected-renderer' }, [
        // Props 信息
        h('div', { class: 'props-header' }, [
          h('h5', '🎯 Actual injectedCode Rendering'),
          h('p', 'This shows the real component rendered from injectedCode:'),
          h('div', { class: 'props-info' }, [
            h('div', { class: 'prop-line' }, [
              h('strong', 'refList: '),
              h('span', { class: 'prop-val' }, JSON.stringify(componentProps.refList))
            ]),
            h('div', { class: 'prop-line' }, [
              h('strong', 'dataChart: '),
              h('span', { class: 'prop-val' }, JSON.stringify(componentProps.dataChart))
            ])
          ])
        ]),

        // 实际组件内容
        h('div', { class: 'component-content' }, [
          h('h4', { style: 'margin: 0 0 16px 0; color: #374151; border-bottom: 1px solid #e5e7eb; padding-bottom: 8px;' }, 'Component Output:'),
          h('div', { class: 'output-area' }, [
            // 渲染 h1
            templateContent.includes('<h1>') ? h('h1', {
              style: 'margin: 0 0 16px 0; color: #1f2937; font-size: 24px;'
            }, msg.value) : null,

            // 渲染 input
            templateContent.includes('<input') ? h('input', {
              type: 'text',
              value: msg.value,
              onInput: (e: any) => {
                msg.value = e.target.value
              },
              style: 'padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; width: 250px;',
              placeholder: 'Type here...'
            }) : null,

            // Props 状态
            h('div', {
              style: 'margin-top: 16px; padding: 12px; background: #f3f4f6; border-radius: 6px; font-size: 12px;'
            }, [
              h('div', { style: 'font-weight: 600; margin-bottom: 4px; color: #374151;' }, 'Props Status:'),
              h('div', { style: 'color: #059669;' }, `✅ refList: ${componentProps.refList.length} items available`),
              h('div', { style: 'color: #059669;' }, `✅ dataChart: ${Object.keys(componentProps.dataChart).length} properties available`),
              h('div', { style: 'margin-top: 8px; color: #059669; font-weight: 600;' }, '🎉 injectedCode rendered successfully!')
            ])
          ])
        ])
      ])
    }
  }
})

// 监听文件变化
watchEffect(() => {
  if (props.show && store.value.activeFile) {
    updateProcessedCode()
  }
})

onMounted(() => {
  if (props.show) {
    updateProcessedCode()
  }
})
</script>

<template>
  <div v-if="props.show" class="preview2-container">
    <div class="header">
      <h3>Preview2 - Component with Injected Props</h3>
      <p class="description">This preview shows the component after injectRefListToSFC processing</p>
    </div>

    <div v-if="compilationError" class="error-message">
      <h4>Processing Error:</h4>
      <pre>{{ compilationError }}</pre>
    </div>

    <div v-else-if="processedCode" class="content">
      <div class="code-section">
        <h4>Processed Code:</h4>
        <pre class="code-display">{{ processedCode }}</pre>
      </div>

      <div v-if="dynamicComponent" class="component-info">
        <h4>Component Information:</h4>
        <div class="info-wrapper">
          <p><strong>Active File:</strong> {{ dynamicComponent }}</p>
          <p><strong>Props Injected:</strong> dataChart, refList</p>
          <p><strong>Status:</strong> Ready for rendering with injected props</p>
        </div>
      </div>

      <div v-if="actualRenderComponent" class="rendered-component">
        <h4>Rendered Component:</h4>
        <div class="component-wrapper">
          <component
            :is="actualRenderComponent"
            :ref-list="refList || ['demo-item-1', 'demo-item-2', 'demo-item-3']"
            :data-chart="dataChart || { type: 'bar', data: [10, 20, 30], title: 'Demo Chart' }"
            :style="styleChartName"
          />
        </div>
      </div>
    </div>

    <div v-else class="no-component">
      <p>No Vue component to preview</p>
    </div>
  </div>
</template>

<style scoped>
.preview2-container {
  width: 100%;
  height: 100%;
  padding: 16px;
  box-sizing: border-box;
  overflow: auto;
}

.header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.header h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.description {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.code-section, .component-info, .rendered-component {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
}

.code-section h4, .component-info h4, .rendered-component h4 {
  margin: 0;
  padding: 8px 12px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-size: 12px;
  font-weight: 600;
  color: #374151;
}

.code-display {
  margin: 0;
  padding: 12px;
  font-size: 11px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-word;
  background: #f8fafc;
  color: #1e293b;
  max-height: 300px;
  overflow: auto;
}

.info-wrapper {
  padding: 12px;
  background: white;
}

.info-wrapper p {
  margin: 8px 0;
  font-size: 13px;
  line-height: 1.4;
}

.info-wrapper strong {
  color: #374151;
  font-weight: 600;
}

.component-wrapper {
  padding: 16px;
  background: white;
  min-height: 120px;
}

.error-message {
  color: #dc2626;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
}

.error-message h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
}

.error-message pre {
  margin: 0;
  font-size: 12px;
  white-space: pre-wrap;
  word-break: break-word;
}

.no-component {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6b7280;
  font-style: italic;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}

/* 渲染组件的样式 */
.injected-renderer {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 1px solid #f59e0b;
  border-radius: 8px;
  overflow: hidden;
}

.props-header {
  background: rgba(245, 158, 11, 0.1);
  padding: 16px;
  border-bottom: 1px solid #f59e0b;
}

.props-header h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #92400e;
}

.props-header p {
  margin: 0 0 12px 0;
  font-size: 12px;
  color: #b45309;
}

.props-info {
  background: white;
  border-radius: 4px;
  padding: 8px;
  border: 1px solid #fbbf24;
}

.prop-line {
  margin-bottom: 4px;
  font-size: 11px;
  line-height: 1.4;
}

.prop-line:last-child {
  margin-bottom: 0;
}

.prop-line strong {
  color: #92400e;
  font-weight: 600;
}

.prop-val {
  color: #1e40af;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #eff6ff;
  padding: 1px 4px;
  border-radius: 2px;
  font-size: 10px;
}

.component-content {
  padding: 16px;
  background: white;
}

.output-area {
  background: #f8fafc;
  border: 2px dashed #cbd5e1;
  border-radius: 8px;
  padding: 20px;
  margin-top: 8px;
}

.output-area h1 {
  font-family: system-ui, sans-serif;
}

.output-area input {
  transition: border-color 0.2s, box-shadow 0.2s;
}

.output-area input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
</style>
