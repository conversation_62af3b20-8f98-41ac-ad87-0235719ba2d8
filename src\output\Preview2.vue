<script setup lang="ts">
import { computed, h, inject, onMounted, reactive, ref, shallowRef, watch, watchEffect } from 'vue'
import { injectKeyProps } from '../types'
import { injectRefListToSFC } from '../transform'

const props = defineProps<{
  show: boolean
  refList?: Array<any> | Object
  dataChart?: Object
  styleChartName?: Object
}>()

const { store } = inject(injectKeyProps)!

// 响应式数据
const processedCode = ref<string>('')
const compilationError = ref<string>('')
const renderedComponent = shallowRef<any>(null)
const renderError = ref<string>('')

// 获取处理后的代码和渲染组件
const updateProcessedCode = async () => {
  try {
    const activeFile = store.value.activeFile
    if (!activeFile || !activeFile.filename.endsWith('.vue')) {
      processedCode.value = ''
      compilationError.value = ''
      renderedComponent.value = null
      renderError.value = ''
      return
    }

    // 使用 injectRefListToSFC 处理代码
    const injectedCode = injectRefListToSFC(activeFile.code, activeFile.filename)
    processedCode.value = injectedCode
    compilationError.value = ''

    console.log('Preview2 - Original code:', activeFile.code)
    console.log('Preview2 - Processed code:', injectedCode)

    // 尝试渲染组件
    await renderComponent(activeFile)

  } catch (error: any) {
    console.error('Code processing error:', error)
    compilationError.value = error.message || 'Unknown processing error'
    processedCode.value = ''
    renderedComponent.value = null
    renderError.value = error.message || 'Unknown processing error'
  }
}

// 渲染组件 - 创建一个模拟原始组件的演示
const renderComponent = async (activeFile: any) => {
  try {
    renderError.value = ''

    // 检查是否有编译后的 JS 代码
    if (!activeFile.compiled || !activeFile.compiled.js) {
      renderError.value = 'Component not compiled yet'
      renderedComponent.value = null
      return
    }

    // 创建一个模拟原始组件的演示组件
    const demoComponent = {
      name: 'Preview2ActualComponent',
      props: {
        refList: { type: [Array, Object], default: () => ['demo-item-1', 'demo-item-2'] },
        dataChart: { type: Object, default: () => ({ type: 'bar', data: [10, 20, 30] }) }
      },
      setup(props: any) {
        // 模拟原始组件的逻辑
        const msg = ref('Hello World!')

        return () => h('div', { class: 'actual-component-demo' }, [
          // Props 信息
          h('div', { class: 'props-header' }, [
            h('h5', '🎯 Actual Component Rendering'),
            h('p', 'This shows the original component with injected props:'),
            h('div', { class: 'props-values' }, [
              h('div', { class: 'prop-line' }, [
                h('strong', 'refList: '),
                h('span', { class: 'prop-val' }, JSON.stringify(props.refList))
              ]),
              h('div', { class: 'prop-line' }, [
                h('strong', 'dataChart: '),
                h('span', { class: 'prop-val' }, JSON.stringify(props.dataChart))
              ])
            ])
          ]),

          // 原始组件内容
          h('div', { class: 'original-content' }, [
            h('h4', { style: 'margin: 0 0 12px 0; color: #374151; border-bottom: 1px solid #e5e7eb; padding-bottom: 8px;' }, 'Original Component Output:'),

            // 这里是原始组件的实际内容
            h('div', { class: 'component-output' }, [
              // h1 标题
              h('h1', {
                style: 'margin: 0 0 16px 0; color: #1f2937; font-size: 24px;'
              }, msg.value),

              // input 输入框
              h('input', {
                type: 'text',
                value: msg.value,
                onInput: (e: any) => {
                  msg.value = e.target.value
                },
                style: 'padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; width: 200px;',
                placeholder: 'Type here...'
              }),

              // 显示 props 使用情况
              h('div', {
                style: 'margin-top: 16px; padding: 12px; background: #f3f4f6; border-radius: 6px; font-size: 12px;'
              }, [
                h('div', { style: 'font-weight: 600; margin-bottom: 4px;' }, 'Props Usage in Component:'),
                h('div', `✅ refList prop available: ${props.refList.length} items`),
                h('div', `✅ dataChart prop available: ${Object.keys(props.dataChart).length} properties`),
                h('div', { style: 'margin-top: 8px; color: #059669;' }, '🎉 Component successfully receives injected props!')
              ])
            ])
          ])
        ])
      }
    }

    renderedComponent.value = demoComponent
    console.log('Preview2 - Actual component demo created')

  } catch (error: any) {
    console.error('Component rendering error:', error)
    renderError.value = error.message || 'Unknown rendering error'
    renderedComponent.value = null
  }
}

// 计算动态组件
const dynamicComponent = computed(() => {
  const activeFile = store.value.activeFile
  if (!activeFile || !activeFile.filename.endsWith('.vue')) {
    return null
  }

  // 简化处理：直接返回文件名作为组件标识
  // 这将在模板中被解析为实际的组件
  return activeFile.filename
})

// 监听文件变化
watchEffect(() => {
  if (props.show && store.value.activeFile) {
    updateProcessedCode()
  }
})

onMounted(() => {
  if (props.show) {
    updateProcessedCode()
  }
})
</script>

<template>
  <div v-if="props.show" class="preview2-container">
    <div class="header">
      <h3>Preview2 - Component with Injected Props</h3>
      <p class="description">This preview shows the component after injectRefListToSFC processing</p>
    </div>

    <div v-if="compilationError" class="error-message">
      <h4>Processing Error:</h4>
      <pre>{{ compilationError }}</pre>
    </div>

    <div v-else-if="processedCode" class="content">
      <div class="code-section">
        <h4>Processed Code:</h4>
        <pre class="code-display">{{ processedCode }}</pre>
      </div>

      <div v-if="dynamicComponent" class="component-info">
        <h4>Component Information:</h4>
        <div class="info-wrapper">
          <p><strong>Active File:</strong> {{ dynamicComponent }}</p>
          <p><strong>Props Injected:</strong> dataChart, refList</p>
          <p><strong>Status:</strong> Ready for rendering with injected props</p>
        </div>
      </div>

      <div v-if="renderError" class="render-error">
        <h4>Render Error:</h4>
        <pre>{{ renderError }}</pre>
      </div>

      <div v-else-if="renderedComponent" class="rendered-component">
        <h4>Rendered Component:</h4>
        <div class="component-preview">
          <component
            :is="renderedComponent"
            :ref-list="refList || ['demo-item-1', 'demo-item-2', 'demo-item-3']"
            :data-chart="dataChart || { type: 'bar', data: [10, 20, 30], title: 'Demo Chart' }"
            :style="styleChartName"
          />
        </div>
      </div>

      <div v-else-if="dynamicComponent" class="render-pending">
        <h4>Component Rendering:</h4>
        <p>Attempting to render component...</p>
      </div>
    </div>

    <div v-else class="no-component">
      <p>No Vue component to preview</p>
    </div>
  </div>
</template>

<style scoped>
.preview2-container {
  width: 100%;
  height: 100%;
  padding: 16px;
  box-sizing: border-box;
  overflow: auto;
}

.header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.header h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.description {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.code-section, .component-info, .rendered-component, .render-pending {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
}

.code-section h4, .component-info h4, .rendered-component h4, .render-pending h4 {
  margin: 0;
  padding: 8px 12px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-size: 12px;
  font-weight: 600;
  color: #374151;
}

.code-display {
  margin: 0;
  padding: 12px;
  font-size: 11px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-word;
  background: #f8fafc;
  color: #1e293b;
  max-height: 300px;
  overflow: auto;
}

.info-wrapper {
  padding: 12px;
  background: white;
}

.info-wrapper p {
  margin: 8px 0;
  font-size: 13px;
  line-height: 1.4;
}

.info-wrapper strong {
  color: #374151;
  font-weight: 600;
}

.note {
  margin-top: 16px;
  padding: 12px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 4px;
}

.note p {
  margin: 0;
  font-size: 12px;
  color: #0369a1;
  line-height: 1.5;
}

.component-preview {
  padding: 16px;
  background: white;
  min-height: 120px;
  border: 1px dashed #d1d5db;
  border-radius: 4px;
  margin: 12px;
}

.render-error {
  border: 1px solid #fecaca;
  border-radius: 6px;
  overflow: hidden;
  background: #fef2f2;
}

.render-error h4 {
  margin: 0;
  padding: 8px 12px;
  background: #fee2e2;
  border-bottom: 1px solid #fecaca;
  font-size: 12px;
  font-weight: 600;
  color: #dc2626;
}

.render-error pre {
  margin: 0;
  padding: 12px;
  font-size: 11px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-word;
  color: #dc2626;
}

.render-pending {
  background: #fffbeb;
  border-color: #fed7aa;
}

.render-pending h4 {
  background: #fef3c7;
  border-bottom-color: #fed7aa;
  color: #d97706;
}

.render-pending p {
  margin: 0;
  padding: 12px;
  font-size: 13px;
  color: #d97706;
  font-style: italic;
}

/* Preview2 Demo Component Styles */
.preview2-demo {
  padding: 16px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 8px;
  border: 1px solid #0ea5e9;
}

.demo-header h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #0c4a6e;
}

.demo-header p {
  margin: 0 0 16px 0;
  font-size: 12px;
  color: #075985;
}

.props-display {
  background: white;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
  border: 1px solid #bae6fd;
}

.prop-item {
  margin-bottom: 8px;
  font-size: 13px;
  line-height: 1.4;
}

.prop-item:last-child {
  margin-bottom: 0;
}

.prop-item strong {
  color: #0c4a6e;
  font-weight: 600;
}

.prop-value {
  color: #1e40af;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  background: #eff6ff;
  padding: 2px 6px;
  border-radius: 3px;
  margin-left: 8px;
}

.demo-note {
  background: #dcfce7;
  border: 1px solid #bbf7d0;
  border-radius: 6px;
  padding: 12px;
}

.demo-note p {
  margin: 4px 0;
  font-size: 12px;
  color: #166534;
  line-height: 1.4;
}

.demo-note p:first-child {
  margin-top: 0;
}

.demo-note p:last-child {
  margin-bottom: 0;
}

/* Actual Component Demo Styles */
.actual-component-demo {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 1px solid #f59e0b;
  border-radius: 8px;
  overflow: hidden;
}

.props-header {
  background: rgba(245, 158, 11, 0.1);
  padding: 16px;
  border-bottom: 1px solid #f59e0b;
}

.props-header h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #92400e;
}

.props-header p {
  margin: 0 0 12px 0;
  font-size: 12px;
  color: #b45309;
}

.props-values {
  background: white;
  border-radius: 4px;
  padding: 8px;
  border: 1px solid #fbbf24;
}

.prop-line {
  margin-bottom: 4px;
  font-size: 11px;
  line-height: 1.4;
}

.prop-line:last-child {
  margin-bottom: 0;
}

.prop-line strong {
  color: #92400e;
  font-weight: 600;
}

.prop-val {
  color: #1e40af;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #eff6ff;
  padding: 1px 4px;
  border-radius: 2px;
  font-size: 10px;
}

.original-content {
  padding: 16px;
  background: white;
}

.component-output {
  background: #f8fafc;
  border: 2px dashed #cbd5e1;
  border-radius: 8px;
  padding: 20px;
  margin-top: 8px;
}

.component-output h1 {
  font-family: system-ui, sans-serif;
}

.component-output input {
  transition: border-color 0.2s, box-shadow 0.2s;
}

.component-output input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.error-message {
  color: #dc2626;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
}

.error-message h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
}

.error-message pre {
  margin: 0;
  font-size: 12px;
  white-space: pre-wrap;
  word-break: break-word;
}

.no-component {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6b7280;
  font-style: italic;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}
</style>
